import { defineS<PERSON> } from 'pinia';
import { ref, computed, onUnmounted } from 'vue';
import { useAuthStore } from './auth';

export const useMessagesStore = defineStore('messages', () => {
  // State
  const conversations = ref([]);
  const currentConversation = ref(null);
  const messages = ref([]);
  const loading = ref(false);
  const error = ref(null);

  // Simple polling
  const pollingInterval = ref(null);
  const pollingFrequency = 3000; // 3 seconds
  const lastMessageId = ref(null);

  // Getters
  const allConversations = computed(() => conversations.value);
  const currentMessages = computed(() => messages.value);
  const conversationDetails = computed(() => currentConversation.value);

  // Actions
  async function fetchConversations() {
    const authStore = useAuthStore();
    if (!authStore.token) {
      console.warn('No auth token available for fetching conversations');
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages-simple/conversations', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch conversations: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch conversations');
      }

      // Update conversations
      conversations.value = data.data?.conversations || [];
      return conversations.value;
    } catch (err) {
      console.error('Error fetching conversations:', err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchMessages(conversationId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch(`/.netlify/functions/messages-simple/messages?conversationId=${conversationId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch messages');
      }

      // Update messages
      messages.value = data.data.messages || [];
      
      // Update last message ID for polling
      if (messages.value.length > 0) {
        lastMessageId.value = messages.value[messages.value.length - 1]._id;
      }

      return messages.value;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function getOrCreateConversation(participantId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages-simple/create-conversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ participantId })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create conversation');
      }

      // Set current conversation
      currentConversation.value = data.data.conversation;

      // Fetch messages for the conversation
      await fetchMessages(data.data.conversation._id);

      // Refresh conversations list
      await fetchConversations();

      return data.data.conversation;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function sendMessage(content) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    if (!currentConversation.value) throw new Error('No active conversation');
    if (!content || content.trim() === '') throw new Error('Message content is required');

    const conversationId = currentConversation.value._id;
    const sanitizedContent = content.trim();

    // Add optimistic message to UI immediately
    const optimisticMessage = {
      _id: `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      conversationId,
      sender: authStore.user,
      content: sanitizedContent,
      createdAt: new Date().toISOString(),
      isOptimistic: true
    };

    messages.value = [...messages.value, optimisticMessage];
    updateConversationWithNewMessage(conversationId, optimisticMessage);

    try {
      const response = await fetch('/.netlify/functions/messages-simple/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          conversationId,
          content: sanitizedContent
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }

      // Replace optimistic message with real message
      const messageIndex = messages.value.findIndex(m => m._id === optimisticMessage._id);
      if (messageIndex !== -1) {
        messages.value[messageIndex] = {
          ...data.data.message,
          isOptimistic: false
        };
      }

      // Update last message ID
      lastMessageId.value = data.data.message._id;

      return data.data.message;
    } catch (err) {
      // Remove optimistic message on error
      messages.value = messages.value.filter(m => m._id !== optimisticMessage._id);
      error.value = err.message;
      throw err;
    }
  }

  function setCurrentConversation(conversation) {
    // Stop any existing polling
    stopPolling();

    currentConversation.value = conversation;
    messages.value = [];

    if (conversation) {
      fetchMessages(conversation._id).then(() => {
        // Start polling for new messages
        startPolling(conversation._id);
      });
    }
  }

  // Simple polling for new messages
  function startPolling(conversationId) {
    stopPolling();

    pollingInterval.value = setInterval(async () => {
      try {
        await pollNewMessages(conversationId);
      } catch (err) {
        console.error('Error polling for new messages:', err);
      }
    }, pollingFrequency);

    // Clean up on component unmount
    onUnmounted(() => {
      stopPolling();
    });
  }

  function stopPolling() {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
  }

  // Poll for new messages
  async function pollNewMessages(conversationId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const sinceParam = lastMessageId.value ? `&since=${lastMessageId.value}` : '';
      const response = await fetch(`/.netlify/functions/messages-simple/new-messages?conversationId=${conversationId}${sinceParam}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return; // Silently fail polling errors
      }

      // If there are new messages, add them to the messages array
      if (data.data.messages && data.data.messages.length > 0) {
        // Add new messages
        messages.value = [...messages.value, ...data.data.messages];

        // Update last message ID
        const latestMessage = data.data.messages[data.data.messages.length - 1];
        lastMessageId.value = latestMessage._id;

        // Update conversation in the conversations array
        updateConversationWithNewMessage(conversationId, latestMessage);
      }
    } catch (err) {
      // Silently fail polling errors to avoid spam
      console.error('Error polling for new messages:', err);
    }
  }

  // Update conversation with new message
  function updateConversationWithNewMessage(conversationId, message) {
    const conversationIndex = conversations.value.findIndex(c => c._id === conversationId);
    if (conversationIndex !== -1) {
      const conversation = conversations.value[conversationIndex];

      // Update last message
      conversation.lastMessage = {
        _id: message._id,
        sender: message.sender,
        content: message.content,
        createdAt: message.createdAt
      };

      // Move conversation to top
      conversations.value.splice(conversationIndex, 1);
      conversations.value.unshift(conversation);
    }
  }

  return {
    // State
    conversations,
    currentConversation,
    messages,
    loading,
    error,

    // Getters
    allConversations,
    currentMessages,
    conversationDetails,

    // Actions
    fetchConversations,
    fetchMessages,
    getOrCreateConversation,
    sendMessage,
    setCurrentConversation,
    startPolling,
    stopPolling
  };
});
