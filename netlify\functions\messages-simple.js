const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection
let cachedClient = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

// Helper functions
const success = (data) => ({
  statusCode: 200,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ success: true, data })
});

const error = (message, statusCode = 400) => ({
  statusCode,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ success: false, error: message })
});

// Auth middleware
function requireAuth(handler) {
  return async (event) => {
    const authHeader = event.headers.authorization || event.headers.Authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return error('Authorization token required', 401);
    }

    const token = authHeader.substring(7);
    try {
      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      event.user = payload;
      return handler(event);
    } catch (err) {
      return error('Invalid token', 401);
    }
  };
}

// Get conversations
const getConversations = requireAuth(async (event) => {
  const userId = event.user._id;

  try {
    const client = await connectToDatabase();
    const db = client.db('netuark');

    const conversations = await db.collection('conversations')
      .find({ participants: new ObjectId(userId) })
      .sort({ updatedAt: -1 })
      .toArray();

    // Populate participant info and last message
    for (let conv of conversations) {
      // Get other participant
      const otherParticipantId = conv.participants.find(p => p.toString() !== userId);
      const participant = await db.collection('users').findOne(
        { _id: otherParticipantId },
        { projection: { username: 1, displayName: 1, profilePicture: 1 } }
      );

      conv.participant = participant;

      // Get last message
      const lastMessage = await db.collection('messages')
        .findOne(
          { conversationId: conv._id },
          { sort: { createdAt: -1 } }
        );

      if (lastMessage) {
        const sender = await db.collection('users').findOne(
          { _id: lastMessage.senderId },
          { projection: { username: 1, displayName: 1 } }
        );
        conv.lastMessage = {
          _id: lastMessage._id,
          sender,
          content: lastMessage.content,
          createdAt: lastMessage.createdAt
        };
      }
    }

    return success({ conversations });
  } catch (err) {
    console.error('Error fetching conversations:', err);
    return error('Failed to fetch conversations');
  }
});

// Get messages for a conversation
const getMessages = requireAuth(async (event) => {
  const userId = event.user._id;
  const conversationId = event.queryStringParameters?.conversationId;

  if (!conversationId) {
    return error('Conversation ID is required');
  }

  try {
    const client = await connectToDatabase();
    const db = client.db('netuark');

    // Verify user is part of conversation
    const conversation = await db.collection('conversations').findOne({
      _id: new ObjectId(conversationId),
      participants: new ObjectId(userId)
    });

    if (!conversation) {
      return error('Conversation not found', 404);
    }

    // Get messages
    const messages = await db.collection('messages')
      .find({ conversationId: new ObjectId(conversationId) })
      .sort({ createdAt: 1 })
      .limit(100)
      .toArray();

    // Populate sender info
    for (let message of messages) {
      const sender = await db.collection('users').findOne(
        { _id: message.senderId },
        { projection: { username: 1, displayName: 1, profilePicture: 1 } }
      );
      message.sender = sender;
    }

    return success({ messages });
  } catch (err) {
    console.error('Error fetching messages:', err);
    return error('Failed to fetch messages');
  }
});

exports.handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      }
    };
  }

  const path = event.path.replace('/.netlify/functions/messages-simple', '');
  const method = event.httpMethod;

  try {
    // Route requests
    if (method === 'GET' && path === '/conversations') {
      return await getConversations(event);
    } else if (method === 'GET' && path === '/messages') {
      return await getMessages(event);
    } else if (method === 'GET' && path === '/new-messages') {
      return await getNewMessages(event);
    } else if (method === 'POST' && path === '/create-conversation') {
      return await createConversation(event);
    } else if (method === 'POST' && path === '/send') {
      return await sendMessage(event);
    } else {
      return error('Not found', 404);
    }
  } catch (err) {
    console.error('Handler error:', err);
    return error('Internal server error', 500);
  }
};

// Get new messages since last ID
const getNewMessages = requireAuth(async (event) => {
  const userId = event.user._id;
  const conversationId = event.queryStringParameters?.conversationId;
  const since = event.queryStringParameters?.since;

  if (!conversationId) {
    return error('Conversation ID is required');
  }

  try {
    const client = await connectToDatabase();
    const db = client.db('netuark');

    // Build query
    const query = { conversationId: new ObjectId(conversationId) };
    if (since) {
      query._id = { $gt: new ObjectId(since) };
    }

    // Get new messages
    const messages = await db.collection('messages')
      .find(query)
      .sort({ createdAt: 1 })
      .toArray();

    // Populate sender info
    for (let message of messages) {
      const sender = await db.collection('users').findOne(
        { _id: message.senderId },
        { projection: { username: 1, displayName: 1, profilePicture: 1 } }
      );
      message.sender = sender;
    }

    return success({ messages });
  } catch (err) {
    console.error('Error fetching new messages:', err);
    return error('Failed to fetch new messages');
  }
});

// Create or get conversation
const createConversation = requireAuth(async (event) => {
  const userId = event.user._id;
  const { participantId } = JSON.parse(event.body);

  if (!participantId) {
    return error('Participant ID is required');
  }

  try {
    const client = await connectToDatabase();
    const db = client.db('netuark');

    // Check if conversation already exists
    const existingConversation = await db.collection('conversations').findOne({
      participants: { $all: [new ObjectId(userId), new ObjectId(participantId)] }
    });

    if (existingConversation) {
      return success({ conversation: existingConversation });
    }

    // Create new conversation
    const conversation = {
      participants: [new ObjectId(userId), new ObjectId(participantId)],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection('conversations').insertOne(conversation);
    conversation._id = result.insertedId;

    return success({ conversation });
  } catch (err) {
    console.error('Error creating conversation:', err);
    return error('Failed to create conversation');
  }
});

// Send message
const sendMessage = requireAuth(async (event) => {
  const userId = event.user._id;
  const { conversationId, content } = JSON.parse(event.body);

  if (!conversationId || !content) {
    return error('Conversation ID and content are required');
  }

  try {
    const client = await connectToDatabase();
    const db = client.db('netuark');

    // Verify user is part of conversation
    const conversation = await db.collection('conversations').findOne({
      _id: new ObjectId(conversationId),
      participants: new ObjectId(userId)
    });

    if (!conversation) {
      return error('Conversation not found', 404);
    }

    // Create message
    const message = {
      conversationId: new ObjectId(conversationId),
      senderId: new ObjectId(userId),
      content: content.trim(),
      createdAt: new Date()
    };

    const result = await db.collection('messages').insertOne(message);
    message._id = result.insertedId;

    // Update conversation timestamp
    await db.collection('conversations').updateOne(
      { _id: new ObjectId(conversationId) },
      { $set: { updatedAt: new Date() } }
    );

    // Get sender info
    const sender = await db.collection('users').findOne(
      { _id: new ObjectId(userId) },
      { projection: { username: 1, displayName: 1, profilePicture: 1 } }
    );
    message.sender = sender;

    return success({ message });
  } catch (err) {
    console.error('Error sending message:', err);
    return error('Failed to send message');
  }
});
