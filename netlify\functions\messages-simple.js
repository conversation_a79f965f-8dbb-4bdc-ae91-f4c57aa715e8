const { success, error } = require('./utils/response');

// Simple test handler to verify the function works
exports.handler = async (event, context) => {
  // Add CORS headers for all requests
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    console.log('Simple messages handler called');
    console.log('Event:', {
      httpMethod: event.httpMethod,
      path: event.path,
      headers: event.headers
    });

    // Test basic functionality
    const result = success({
      message: 'Simple messages API is working',
      timestamp: new Date().toISOString(),
      method: event.httpMethod,
      path: event.path,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        MONGODB_URI_EXISTS: !!process.env.MONGODB_URI
      }
    });

    return {
      ...result,
      headers
    };

  } catch (err) {
    console.error('Simple messages handler error:', err);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: err.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
