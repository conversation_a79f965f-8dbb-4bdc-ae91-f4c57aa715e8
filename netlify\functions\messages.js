const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Main handler function
exports.handler = async (event, context) => {
  // Add CORS headers for all requests
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const { httpMethod, path } = event;
  const pathSegments = path.split('/').filter(Boolean);
  const action = pathSegments[pathSegments.length - 1];

  console.log(`Messages API: ${httpMethod} ${path} - Action: ${action}`);
  console.log('Environment variables check:', {
    NODE_ENV: process.env.NODE_ENV,
    MONGODB_URI_EXISTS: !!process.env.MONGODB_URI,
    DB_NAME: process.env.DB_NAME
  });

  try {
    let result;

    switch (httpMethod) {
      case 'GET':
        if (action === 'conversations') {
          result = await exports.getConversations(event);
        } else if (action === 'messages') {
          result = await exports.getMessages(event);
        } else if (action === 'test') {
          result = await exports.testConnection(event);
        } else {
          result = error('Invalid GET endpoint', 404);
        }
        break;

      case 'POST':
        if (action === 'create-conversation') {
          result = await exports.getOrCreateConversation(event);
        } else if (action === 'send') {
          result = await exports.sendMessage(event);
        } else {
          result = error('Invalid POST endpoint', 404);
        }
        break;

      default:
        result = error('Method not allowed', 405);
    }

    // Add CORS headers to the result
    return {
      ...result,
      headers: {
        ...headers,
        ...result.headers
      }
    };

  } catch (err) {
    console.error('Messages handler error:', err);
    console.error('Error stack:', err.stack);
    console.error('Event details:', {
      httpMethod: event.httpMethod,
      path: event.path,
      headers: event.headers,
      queryStringParameters: event.queryStringParameters
    });
    
    // Return more specific error information
    const statusCode = err.statusCode || 500;
    const errorMessage = err.message || 'Internal server error';
    
    return {
      statusCode,
      headers,
      body: JSON.stringify({
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? err.stack : undefined,
        timestamp: new Date().toISOString()
      })
    };
  }
};

// Test connection endpoint
exports.testConnection = handleAsync(async (event) => {
  try {
    const { checkDatabaseStatus } = require('./utils/db');
    const dbStatus = await checkDatabaseStatus();
    
    return success({
      message: 'Messages API is working',
      timestamp: new Date().toISOString(),
      database: dbStatus,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        MONGODB_URI_EXISTS: !!process.env.MONGODB_URI,
        DB_NAME: process.env.DB_NAME
      }
    });
  } catch (err) {
    console.error('Test connection error:', err);
    return error(`Test failed: ${err.message}`, 500);
  }
});

// Get conversations
exports.getConversations = handleAsync(requireAuth(async (event) => {
  try {
    // Get user ID from token
    const userId = event.user._id;
    console.log(`Getting conversations for user: ${userId}`);
    
    // Validate user ID
    if (!userId) {
      console.error('User ID is missing from token');
      return error('Invalid user authentication', 401);
    }

    // Test database connection first
    try {
      const { checkDatabaseStatus } = require('./utils/db');
      const dbStatus = await checkDatabaseStatus();
      if (!dbStatus.connected) {
        console.error('Database not connected:', dbStatus);
        return error('Database connection failed. Please try again in a moment.', 503);
      }
    } catch (dbError) {
      console.error('Database status check failed:', dbError);
      return error('Database service unavailable. Please try again in a moment.', 503);
    }

    // For now, return empty conversations array to test basic functionality
    const conversations = [];

    console.log(`Found ${conversations.length} conversations for user ${userId}`);

    // Return conversations with proper structure
    return success({ 
      conversations,
      count: conversations.length,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error in getConversations:', err);
    return error(`Failed to fetch conversations: ${err.message}`, 500);
  }
}));

// Create or get conversation
exports.getOrCreateConversation = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { participantId } = JSON.parse(event.body);

  // Validate input
  if (!participantId) {
    return error('Participant ID is required');
  }

  // For now, return a simple response
  return success({ 
    message: 'Conversation creation not yet implemented',
    userId,
    participantId
  });
}));

// Send message
exports.sendMessage = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { conversationId, content } = JSON.parse(event.body);

  // Validate input
  if (!conversationId || !content) {
    return error('Conversation ID and content are required');
  }

  // For now, return a simple response
  return success({ 
    message: 'Message sending not yet implemented',
    userId,
    conversationId,
    content
  });
}));

// Get messages
exports.getMessages = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get conversation ID from query parameters
  const conversationId = event.queryStringParameters?.conversationId;

  // Validate input
  if (!conversationId) {
    return error('Conversation ID is required');
  }

  // For now, return empty messages array
  return success({ 
    messages: [],
    page: 1,
    limit: 20
  });
}));
